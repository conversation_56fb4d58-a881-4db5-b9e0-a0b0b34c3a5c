# PowerShell script to extract text from image using Windows OCR
param(
    [Parameter(Mandatory=$true)]
    [string]$ImagePath
)

# Add required assemblies
Add-Type -AssemblyName System.Runtime.WindowsRuntime
Add-Type -AssemblyName System.Drawing

# Import Windows Runtime types
$null = [Windows.Storage.StorageFile, Windows.Storage, ContentType=WindowsRuntime]
$null = [Windows.Media.Ocr.OcrEngine, Windows.Media.Ocr, ContentType=WindowsRuntime]
$null = [Windows.Graphics.Imaging.BitmapDecoder, Windows.Graphics.Imaging, ContentType=WindowsRuntime]

# Function to convert .NET task to PowerShell
function Await($WinRtTask, $ResultType) {
    $asTask = ([System.WindowsRuntimeSystemExtensions].GetMethods() | Where-Object { $_.Name -eq 'AsTask' -and $_.GetParameters().Count -eq 1 -and $_.GetParameters()[0].ParameterType.Name -eq 'IAsyncOperation`1' })[0]
    $asTaskGeneric = $asTask.MakeGenericMethod($ResultType)
    $netTask = $asTaskGeneric.Invoke($null, @($WinRtTask))
    $netTask.Wait(-1) | Out-Null
    $netTask.Result
}

try {
    Write-Host "Starting OCR process for: $ImagePath"
    
    # Check if file exists
    if (-not (Test-Path $ImagePath)) {
        throw "Image file not found: $ImagePath"
    }
    
    # Get absolute path
    $absolutePath = Resolve-Path $ImagePath
    Write-Host "Processing file: $absolutePath"
    
    # Create StorageFile from path
    $storageFile = Await ([Windows.Storage.StorageFile]::GetFileFromPathAsync($absolutePath)) ([Windows.Storage.StorageFile])
    
    # Get available OCR languages
    Write-Host "Getting available OCR languages..."
    $availableLanguages = [Windows.Media.Ocr.OcrEngine]::AvailableRecognizerLanguages
    Write-Host "Available OCR languages: $($availableLanguages.DisplayName -join ', ')"

    # Try to create OCR engine with the first available language
    $ocrEngine = $null
    foreach ($lang in $availableLanguages) {
        Write-Host "Trying to create OCR engine for: $($lang.DisplayName)"
        $ocrEngine = [Windows.Media.Ocr.OcrEngine]::TryCreateFromLanguage($lang)
        if ($null -ne $ocrEngine) {
            Write-Host "Successfully created OCR engine for: $($lang.DisplayName)"
            break
        }
    }
    
    if ($null -eq $ocrEngine) {
        throw "Could not create OCR engine"
    }
    
    Write-Host "OCR Engine created successfully"
    
    # Open the image file
    $stream = Await ($storageFile.OpenAsync([Windows.Storage.FileAccessMode]::Read)) ([Windows.Storage.Streams.IRandomAccessStream])
    
    # Create bitmap decoder
    $decoder = Await ([Windows.Graphics.Imaging.BitmapDecoder]::CreateAsync($stream)) ([Windows.Graphics.Imaging.BitmapDecoder])
    
    # Get the bitmap
    $bitmap = Await ($decoder.GetSoftwareBitmapAsync()) ([Windows.Graphics.Imaging.SoftwareBitmap])
    
    Write-Host "Image loaded successfully"
    
    # Perform OCR
    $ocrResult = Await ($ocrEngine.RecognizeAsync($bitmap)) ([Windows.Media.Ocr.OcrResult])
    
    Write-Host "OCR completed successfully"
    Write-Host "==================== EXTRACTED TEXT ===================="
    
    # Output the recognized text
    $allText = ""
    foreach ($line in $ocrResult.Lines) {
        $lineText = ""
        foreach ($word in $line.Words) {
            $lineText += $word.Text + " "
        }
        $lineText = $lineText.Trim()
        if ($lineText -ne "") {
            Write-Host $lineText
            $allText += $lineText + "`n"
        }
    }
    
    Write-Host "==================== END OF TEXT ===================="
    Write-Host "Total lines extracted: $($ocrResult.Lines.Count)"
    
    # Save to file
    $outputFile = [System.IO.Path]::ChangeExtension($ImagePath, ".txt")
    $allText | Out-File -FilePath $outputFile -Encoding UTF8
    Write-Host "Text saved to: $outputFile"
    
    # Clean up
    $stream.Dispose()
    
} catch {
    Write-Error "Error during OCR process: $($_.Exception.Message)"
    Write-Error $_.Exception.StackTrace
}
